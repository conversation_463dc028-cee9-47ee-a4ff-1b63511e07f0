package test

import (
	"context"
	"testing"
	"time"

	"taskReminder/plugin"
	"taskReminder/plugins/examples"
	"taskReminder/types"
)

// 测试新插件架构
func TestNewPluginArchitecture(t *testing.T) {
	t.Log("=== 测试新插件架构 ===")

	// 创建新版插件实例
	newPlugin := &examples.SimpleReminderPlugin{}

	// 测试静态信息获取（应该只调用一次）
	staticInfo := newPlugin.GetStaticInfo()
	if staticInfo == nil {
		t.<PERSON>al("静态信息不能为空")
	}
	t.Logf("插件ID: %s", staticInfo.PluginID)
	t.Logf("插件描述: %s", staticInfo.PluginDescription)

	// 测试调度配置获取（应该只调用一次）
	scheduleConfig := newPlugin.GetScheduleConfig()
	if scheduleConfig == nil {
		t.<PERSON><PERSON>("调度配置不能为空")
	}
	t.Logf("调度类型: %v", scheduleConfig.Schedule.Type)
	t.Logf("Cron表达式: %s", scheduleConfig.Schedule.CronExpr)
	t.Logf("是否启用: %v", scheduleConfig.Enabled)

	// 测试内容生成（每次执行时调用）
	ctx := context.Background()
	content, err := newPlugin.GenerateContent(ctx)
	if err != nil {
		t.Fatalf("生成内容失败: %v", err)
	}
	if content == nil {
		t.Fatal("生成的内容不能为空")
	}
	t.Logf("标题: %s", content.Title)
	t.Logf("消息类型数量: %d", len(content.MessageTypes))
	t.Logf("钉钉内容长度: %d", len(content.DingTalkContent))
	t.Logf("邮件内容长度: %d", len(content.EmailContent))

	// 验证内容包含实时信息
	if len(content.Title) == 0 {
		t.Error("标题不能为空")
	}
	if len(content.DingTalkContent) == 0 {
		t.Error("钉钉内容不能为空")
	}
	if len(content.EmailContent) == 0 {
		t.Error("邮件内容不能为空")
	}

	t.Log("✅ 新插件架构测试通过")
}

// 测试插件包装器
func TestPluginWrapper(t *testing.T) {
	t.Log("=== 测试插件包装器 ===")

	// 创建新版插件
	newPlugin := &examples.SimpleReminderPlugin{}
	wrapper := plugin.NewBasePlugin(newPlugin)

	// 测试Plugin接口方法
	name := wrapper.GetName()
	if name == "" {
		t.Error("插件名称不能为空")
	}
	t.Logf("插件名称: %s", name)

	description := wrapper.GetDescription()
	if description == "" {
		t.Error("插件描述不能为空")
	}
	t.Logf("插件描述: %s", description)

	// 测试任务配置获取
	taskConfig := wrapper.GetTaskConfig()
	if taskConfig == nil {
		t.Fatal("任务配置不能为空")
	}
	t.Logf("任务名称: %s", taskConfig.Name)
	t.Logf("是否启用: %v", taskConfig.Enabled)

	// 测试运行时配置获取
	ctx := context.Background()
	runtimeConfig := wrapper.GetRuntimeConfig(ctx)
	if runtimeConfig == nil {
		t.Fatal("运行时配置不能为空")
	}
	t.Logf("运行时消息类型数量: %d", len(runtimeConfig.MessageTypes))

	// 验证运行时配置包含具体的消息配置
	if len(runtimeConfig.MessageTypes) > 0 {
		if runtimeConfig.DingTalk != nil {
			t.Logf("钉钉配置标题: %s", runtimeConfig.DingTalk.Title)
		}
		if runtimeConfig.Email != nil {
			t.Logf("邮件配置主题: %s", runtimeConfig.Email.Subject)
		}
	}

	t.Log("✅ 插件包装器测试通过")
}

// 测试条件性消息发送
func TestConditionalMessaging(t *testing.T) {
	t.Log("=== 测试条件性消息发送 ===")

	// 创建天气提醒插件
	weatherPlugin := &examples.WeatherReminderPlugin{}
	wrapper := plugin.NewBasePlugin(weatherPlugin)

	ctx := context.Background()

	// 多次调用运行时配置，验证条件性发送
	for i := 0; i < 3; i++ {
		runtimeConfig := wrapper.GetRuntimeConfig(ctx)
		if runtimeConfig == nil {
			t.Fatal("运行时配置不能为空")
		}

		t.Logf("第%d次调用 - 消息类型数量: %d", i+1, len(runtimeConfig.MessageTypes))

		// 如果有消息类型，验证配置完整性
		if len(runtimeConfig.MessageTypes) > 0 {
			if runtimeConfig.DingTalk != nil {
				t.Logf("  钉钉消息标题: %s", runtimeConfig.DingTalk.Title)
			}
			if runtimeConfig.Email != nil {
				t.Logf("  邮件主题: %s", runtimeConfig.Email.Subject)
			}
		} else {
			t.Log("  无需发送消息（条件不满足）")
		}

		time.Sleep(100 * time.Millisecond) // 短暂延迟
	}

	t.Log("✅ 条件性消息发送测试通过")
}

// 测试向后兼容性
func TestBackwardCompatibility(t *testing.T) {
	t.Log("=== 测试向后兼容性 ===")

	// 创建一个模拟的旧版插件
	legacyPlugin := &MockLegacyPlugin{}
	wrapper := plugin.NewLegacyBasePlugin(legacyPlugin)

	// 测试基本功能
	name := wrapper.GetName()
	if name == "" {
		t.Error("插件名称不能为空")
	}
	t.Logf("旧版插件名称: %s", name)

	description := wrapper.GetDescription()
	if description == "" {
		t.Error("插件描述不能为空")
	}
	t.Logf("旧版插件描述: %s", description)

	// 测试运行时配置
	ctx := context.Background()
	runtimeConfig := wrapper.GetRuntimeConfig(ctx)
	if runtimeConfig == nil {
		t.Fatal("运行时配置不能为空")
	}
	t.Logf("旧版插件运行时消息类型数量: %d", len(runtimeConfig.MessageTypes))

	t.Log("✅ 向后兼容性测试通过")
}

// 模拟旧版插件
type MockLegacyPlugin struct{}

func (p *MockLegacyPlugin) GetPluginConfig(ctx context.Context) *plugin.BasePluginConfig {
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	title := "旧版插件测试"
	content := "这是旧版插件生成的内容，时间：" + currentTime

	return &plugin.BasePluginConfig{
		PluginID:          "mock_legacy_plugin",
		PluginDescription: "模拟旧版插件",
		TaskConfig: &types.TaskConfig{
			Name:         "旧版插件任务",
			MessageTypes: []types.MessageType{types.DingTalkMessage},
			DingTalk: &types.DingTalkConfig{
				WebhookURL: "https://example.com/webhook",
				Title:      title,
				Text:       content,
				MsgType:    types.DingTalkMarkdown,
			},
			Enabled: true,
			Schedule: types.ScheduleConfig{
				Type:     types.CronSchedule,
				CronExpr: "0 0 9 * * *",
			},
		},
	}
}

// 性能对比测试
func TestPerformanceComparison(t *testing.T) {
	t.Log("=== 性能对比测试 ===")

	// 测试新版插件性能
	newPlugin := &examples.SimpleReminderPlugin{}
	newWrapper := plugin.NewBasePlugin(newPlugin)

	// 测试旧版插件性能
	legacyPlugin := &MockLegacyPlugin{}
	legacyWrapper := plugin.NewLegacyBasePlugin(legacyPlugin)

	ctx := context.Background()

	// 新版插件：注册时的开销
	start := time.Now()
	_ = newWrapper.GetName()
	_ = newWrapper.GetDescription()
	_ = newWrapper.GetTaskConfig()
	newRegistrationTime := time.Since(start)

	// 旧版插件：注册时的开销
	start = time.Now()
	_ = legacyWrapper.GetName()
	_ = legacyWrapper.GetDescription()
	_ = legacyWrapper.GetTaskConfig()
	legacyRegistrationTime := time.Since(start)

	t.Logf("新版插件注册时间: %v", newRegistrationTime)
	t.Logf("旧版插件注册时间: %v", legacyRegistrationTime)

	// 执行时的性能对比
	start = time.Now()
	for i := 0; i < 10; i++ {
		_ = newWrapper.GetRuntimeConfig(ctx)
	}
	newExecutionTime := time.Since(start)

	start = time.Now()
	for i := 0; i < 10; i++ {
		_ = legacyWrapper.GetRuntimeConfig(ctx)
	}
	legacyExecutionTime := time.Since(start)

	t.Logf("新版插件执行时间(10次): %v", newExecutionTime)
	t.Logf("旧版插件执行时间(10次): %v", legacyExecutionTime)

	t.Log("✅ 性能对比测试完成")
}

// 基准测试
func BenchmarkNewPluginArchitecture(b *testing.B) {
	newPlugin := &examples.SimpleReminderPlugin{}
	wrapper := plugin.NewBasePlugin(newPlugin)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = wrapper.GetRuntimeConfig(ctx)
	}
}

func BenchmarkLegacyPluginArchitecture(b *testing.B) {
	legacyPlugin := &MockLegacyPlugin{}
	wrapper := plugin.NewLegacyBasePlugin(legacyPlugin)
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = wrapper.GetRuntimeConfig(ctx)
	}
}
